{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "sourceMap": false, "removeComments": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": "./src", "paths": {"@core/*": ["core/*"], "@audio/*": ["audio/*"], "@services/*": ["services/*"], "@interfaces/*": ["interfaces/*"], "@commands/*": ["commands/*"], "@views/*": ["views/*"], "@ipc/*": ["ipc/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test-cases", "**/*.test.ts"]}